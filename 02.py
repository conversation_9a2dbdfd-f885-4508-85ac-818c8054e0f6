import re
import logging
from datetime import datetime
import pandas as pd
from collections import defaultdict
import time
# 设置日志记录器
logger = logging.getLogger("data_extraction")
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(message)s')
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)


def split_combined_fields(value):
    """拆分粘连字段（如 '22749.0-4.667+0' -> ['22749.0', '-4.667+0'] 或 '2.160+1-0.216' -> ['2.160+1', '-0.216']）"""
    parts = []
    # 匹配科学计数法或普通负值（如 -4.667+0 或 -0.216）
    pattern = re.compile(r'(-\d+\.\d+(?:[+-]\d+)?)')  # 兼容科学计数法和普通负值
    last_end = 0
    for match in pattern.finditer(value):
        # 添加负号前的部分（如果有）
        if match.start() > last_end:
            parts.append(value[last_end:match.start()])
        # 添加负值部分
        parts.append(match.group(1))
        last_end = match.end()
    # 添加剩余部分（如果有）
    if last_end < len(value):
        parts.append(value[last_end:])
    return parts if parts else [value]


def extract_data(input_file):
    # 读取文件内容
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # 提取时间记录（原逻辑保持不变）
    sim_times = []
    logger.info("\n找到的时间点:")
    for idx, line in enumerate(lines):
        if "SIMULATION ending at" in line:
            match = re.search(r"SIMULATION ending at\s+(\d{4}/\s*\d{1,2}/\s*\d{1,2}):\s*(\d+.\d+)", line)
            print(match)
            if match:
                date_str = match.group(1).replace(" ", "")
                time_str = match.group(2)
                try:
                    date_obj = datetime.strptime(date_str, "%Y/%m/%d")
                    hour_val = float(time_str)
                    sim_times.append((date_obj.strftime("%Y/%m/%d"), hour_val, idx))
                    logger.info(f"日期: {date_str}, 时间: {time_str}")
                except Exception as e:
                    logger.warning(f"[!] 解析失败: {match.group(1)} {time_str}, 错误: {e}")

    # 匹配目标时间点（原逻辑保持不变）
    target_hours = list(range(0, 24, 2))
    sim_by_date = defaultdict(list)
    for date, hour, idx in sim_times:
        sim_by_date[date].append((hour, idx))

    selected_points = []
    logger.info("\n选择的时间点:")
    for date in sorted(sim_by_date):
        points = sim_by_date[date]
        for target in target_hours:
            closest = min(points, key=lambda x: abs(x[0] - target))
            diff = abs(closest[0] - target)
            if diff <= 1:
                selected_points.append((date, closest[0], closest[1]))
                logger.info(f"目标时间: {target:02d}:00, 匹配到: {date} {closest[0]:.4f}")

    # 提取节点数据（核心改进部分）
    extracted_data = []
    for date, hour, line_idx in selected_points:
        data_start = None
        for j in range(line_idx, min(line_idx + 200, len(lines))):
            if "RESULTS at JTIME" in lines[j]:
                for k in range(j, min(j + 10, len(lines))):
                    # 修复空格问题：搜索 "Node Node Identifier"（一个空格）
                    if "Node Node Identifier" in lines[k]:
                        data_start = k + 1
                        logger.info(f"找到数据段，行号: {k}")
                        break
                break
        if data_start is None:
            continue

        logger.info(f"\n提取 {date} {hour:.4f} 的数据:")
        for line in lines[data_start:]:
            if "SIMULATION ending at" in line or "EXTERIOR" in line:
                break
            if line.strip() and line[0].isspace():
                parts = line.split()
                if parts and parts[0].isdigit():
                    node = int(parts[0])
                    # 动态检测节点范围 - 支持不同文件的节点编号
                    if node >= 100:  # 接受所有大于等于100的节点
                        # 动态处理字段粘连（Stat-Flow 和 Area-Vel）
                        processed_parts = []
                        for part in parts:
                            # 拆分粘连字段（如 "22749.0-4.667+0" -> ["22749.0", "-4.667+0"]）
                            split_result = split_combined_fields(part)
                            processed_parts.extend(split_result)

                        # 验证字段数量并提取
                        if len(processed_parts) >= 7:  # Node, middle, Stat, Flow, Area, Vel
                            stat = processed_parts[2]
                            flow = processed_parts[3]
                            area = processed_parts[4]
                            vel = processed_parts[5]
                            elev = processed_parts[7]  # Elev在Depth之后，所以是索引7

                            logger.info(
                                f"Node: {node}, Stat: {stat}, Flow: {flow}, Area: {area}, Vel: {vel}, Elev: {elev}")
                            extracted_data.append((date, hour, node, stat, flow, area, vel, elev))  # 添加elev
                        else:
                            logger.warning(f"字段不足（需8字段，实际{len(processed_parts)}）: {line.strip()}")

        # 保存结果
    if extracted_data:
        df = pd.DataFrame(extracted_data,
                          columns=["Date", "Hour", "Node", "Stat", "Flow", "Area", "Vel", "Elev"])  # 添加Elev列
        df.sort_values(by=["Date", "Hour", "Node"], inplace=True)
        # 从输入文件名生成输出文件名（替换后缀）
        base_name = input_file.rsplit('.', 1)[0]  # 去掉原后缀
        timestamp = datetime.now().strftime("%H%M%S")  # 获取当前时分秒
        csv_file = f"{timestamp}_{base_name}.csv"
        excel_file = f"{timestamp}_{base_name}.xlsx"
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        df.to_excel(excel_file, index=False)

        logger.info("\n📁 数据已保存为 CSV 和 Excel 文件。")
    else:
        logger.info("\n⚠️ 没有提取到任何数据。")


if __name__ == "__main__":
    input_file = 'OUTPUT(2).FEQ'
    extract_data(input_file)
